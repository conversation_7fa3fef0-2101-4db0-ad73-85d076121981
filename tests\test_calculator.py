"""
Unit tests for the Options Calculator API.
"""

import unittest
from datetime import datetime, timedelta
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from options_calculator import LongCallCalculator, LongPutCalculator
from options_calculator.exceptions import ValidationError
from options_calculator.models import OptionCalculationRequest


class TestLongCallCalculator(unittest.TestCase):
    """Test cases for Long Call Calculator."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.calculator = LongCallCalculator()
        self.expiry_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
    
    def tearDown(self):
        """Clean up after tests."""
        self.calculator.close()
    
    def test_basic_long_call_calculation(self):
        """Test basic long call calculation."""
        result = self.calculator.calculate(
            symbol="AAPL",
            current_price=150.00,
            strike_price=155.00,
            option_premium=3.50,
            expiry_date=self.expiry_date,
            contracts=1
        )
        
        self.assertEqual(result.strategy, "Long Call")
        self.assertEqual(result.symbol, "AAPL")
        self.assertEqual(result.current_price, 150.00)
        self.assertEqual(result.strike_price, 155.00)
        self.assertEqual(result.option_premium, 3.50)
        self.assertEqual(result.contracts, 1)
        self.assertEqual(result.total_cost, 350.00)  # 3.50 * 1 * 100
        self.assertEqual(result.breakeven, 158.50)  # 155.00 + 3.50
        self.assertEqual(result.max_loss, 350.00)  # Limited to premium paid
        self.assertIsNone(result.max_profit)  # Unlimited for long calls
        self.assertIsNotNone(result.probability_of_profit)
        self.assertGreater(len(result.scenarios), 0)
    
    def test_multiple_contracts(self):
        """Test calculation with multiple contracts."""
        result = self.calculator.calculate(
            symbol="AAPL",
            current_price=150.00,
            strike_price=155.00,
            option_premium=3.50,
            expiry_date=self.expiry_date,
            contracts=5
        )
        
        self.assertEqual(result.contracts, 5)
        self.assertEqual(result.total_cost, 1750.00)  # 3.50 * 5 * 100
        self.assertEqual(result.max_loss, 1750.00)
    
    def test_validation_errors(self):
        """Test validation error handling."""
        # Test negative current price
        with self.assertRaises(ValidationError):
            self.calculator.calculate(
                symbol="AAPL",
                current_price=-150.00,
                strike_price=155.00,
                option_premium=3.50,
                expiry_date=self.expiry_date
            )
        
        # Test negative strike price
        with self.assertRaises(ValidationError):
            self.calculator.calculate(
                symbol="AAPL",
                current_price=150.00,
                strike_price=-155.00,
                option_premium=3.50,
                expiry_date=self.expiry_date
            )
        
        # Test negative premium
        with self.assertRaises(ValidationError):
            self.calculator.calculate(
                symbol="AAPL",
                current_price=150.00,
                strike_price=155.00,
                option_premium=-3.50,
                expiry_date=self.expiry_date
            )
        
        # Test past expiry date
        past_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        with self.assertRaises(ValidationError):
            self.calculator.calculate(
                symbol="AAPL",
                current_price=150.00,
                strike_price=155.00,
                option_premium=3.50,
                expiry_date=past_date
            )
    
    def test_profit_loss_scenarios(self):
        """Test profit/loss scenario generation."""
        result = self.calculator.calculate(
            symbol="AAPL",
            current_price=150.00,
            strike_price=155.00,
            option_premium=3.50,
            expiry_date=self.expiry_date
        )
        
        scenarios = result.scenarios
        self.assertGreater(len(scenarios), 0)
        
        # Check that scenarios are properly calculated
        for scenario in scenarios:
            self.assertIsInstance(scenario.stock_price, float)
            self.assertIsInstance(scenario.profit_loss, float)
            self.assertIsInstance(scenario.profit_loss_percent, float)
            self.assertIsInstance(scenario.option_value, float)
        
        # Test specific scenario calculations
        # At expiry, if stock is below strike, option is worthless
        below_strike_scenario = next(s for s in scenarios if s.stock_price < result.strike_price)
        self.assertEqual(below_strike_scenario.option_value, 0)
        self.assertEqual(below_strike_scenario.profit_loss, -result.total_cost)
        
        # At expiry, if stock is above breakeven, there should be profit
        above_breakeven_scenario = next(s for s in scenarios if s.stock_price > result.breakeven)
        self.assertGreater(above_breakeven_scenario.profit_loss, 0)


class TestLongPutCalculator(unittest.TestCase):
    """Test cases for Long Put Calculator."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.calculator = LongPutCalculator()
        self.expiry_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
    
    def tearDown(self):
        """Clean up after tests."""
        self.calculator.close()
    
    def test_basic_long_put_calculation(self):
        """Test basic long put calculation."""
        result = self.calculator.calculate(
            symbol="AAPL",
            current_price=150.00,
            strike_price=145.00,
            option_premium=2.75,
            expiry_date=self.expiry_date,
            contracts=1
        )
        
        self.assertEqual(result.strategy, "Long Put")
        self.assertEqual(result.symbol, "AAPL")
        self.assertEqual(result.current_price, 150.00)
        self.assertEqual(result.strike_price, 145.00)
        self.assertEqual(result.option_premium, 2.75)
        self.assertEqual(result.contracts, 1)
        self.assertEqual(result.total_cost, 275.00)  # 2.75 * 1 * 100
        self.assertEqual(result.breakeven, 142.25)  # 145.00 - 2.75
        self.assertEqual(result.max_loss, 275.00)  # Limited to premium paid
        self.assertEqual(result.max_profit, 14225.00)  # (145.00 - 2.75) * 1 * 100
        self.assertIsNotNone(result.probability_of_profit)
        self.assertGreater(len(result.scenarios), 0)
    
    def test_profit_loss_scenarios_put(self):
        """Test profit/loss scenario generation for puts."""
        result = self.calculator.calculate(
            symbol="AAPL",
            current_price=150.00,
            strike_price=145.00,
            option_premium=2.75,
            expiry_date=self.expiry_date
        )
        
        scenarios = result.scenarios
        
        # At expiry, if stock is above strike, option is worthless
        above_strike_scenario = next(s for s in scenarios if s.stock_price > result.strike_price)
        self.assertEqual(above_strike_scenario.option_value, 0)
        self.assertEqual(above_strike_scenario.profit_loss, -result.total_cost)
        
        # At expiry, if stock is below breakeven, there should be profit
        below_breakeven_scenario = next(s for s in scenarios if s.stock_price < result.breakeven)
        self.assertGreater(below_breakeven_scenario.profit_loss, 0)


class TestOptionCalculationRequest(unittest.TestCase):
    """Test cases for OptionCalculationRequest model."""
    
    def test_valid_request(self):
        """Test valid request creation."""
        expiry_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
        request = OptionCalculationRequest(
            symbol="AAPL",
            current_price=150.00,
            strike_price=155.00,
            option_premium=3.50,
            expiry_date=expiry_date,
            contracts=1
        )
        
        self.assertEqual(request.symbol, "AAPL")
        self.assertEqual(request.current_price, 150.00)
        self.assertEqual(request.strike_price, 155.00)
        self.assertEqual(request.option_premium, 3.50)
        self.assertEqual(request.expiry_date, expiry_date)
        self.assertEqual(request.contracts, 1)
    
    def test_symbol_validation(self):
        """Test symbol validation."""
        expiry_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
        
        # Test empty symbol
        with self.assertRaises(ValueError):
            OptionCalculationRequest(
                symbol="",
                current_price=150.00,
                strike_price=155.00,
                option_premium=3.50,
                expiry_date=expiry_date
            )
        
        # Test symbol normalization
        request = OptionCalculationRequest(
            symbol="  aapl  ",
            current_price=150.00,
            strike_price=155.00,
            option_premium=3.50,
            expiry_date=expiry_date
        )
        self.assertEqual(request.symbol, "AAPL")
    
    def test_date_validation(self):
        """Test expiry date validation."""
        # Test invalid date format
        with self.assertRaises(ValueError):
            OptionCalculationRequest(
                symbol="AAPL",
                current_price=150.00,
                strike_price=155.00,
                option_premium=3.50,
                expiry_date="2024/01/19"  # Wrong format
            )


if __name__ == '__main__':
    unittest.main()
