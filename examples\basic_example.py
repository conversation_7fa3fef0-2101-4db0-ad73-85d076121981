#!/usr/bin/env python3
"""
Basic example demonstrating the Options Calculator API usage.
"""

import sys
import os
from datetime import datetime, timedelta

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from options_calculator import LongCallCalculator, LongPutCalculator


def main():
    """Demonstrate basic API usage."""
    print("Options Profit Calculator API - Basic Example")
    print("=" * 50)
    
    # Set up expiry date (30 days from now)
    expiry_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
    
    # Long Call Example
    print("\n1. Long Call Example (AAPL)")
    print("-" * 30)
    
    call_calc = LongCallCalculator()
    try:
        call_result = call_calc.calculate(
            symbol="AAPL",
            current_price=150.00,
            strike_price=155.00,
            option_premium=3.50,
            expiry_date=expiry_date,
            contracts=1
        )
        
        print(f"Strategy: {call_result.strategy}")
        print(f"Break-even: ${call_result.breakeven:.2f}")
        print(f"Max Profit: {'Unlimited' if call_result.max_profit is None else f'${call_result.max_profit:.2f}'}")
        print(f"Max Loss: ${call_result.max_loss:.2f}")
        print(f"Total Cost: ${call_result.total_cost:.2f}")
        if call_result.probability_of_profit:
            print(f"Probability of Profit: {call_result.probability_of_profit:.1f}%")
        
    finally:
        call_calc.close()
    
    # Long Put Example
    print("\n2. Long Put Example (AAPL)")
    print("-" * 30)
    
    put_calc = LongPutCalculator()
    try:
        put_result = put_calc.calculate(
            symbol="AAPL",
            current_price=150.00,
            strike_price=145.00,
            option_premium=2.75,
            expiry_date=expiry_date,
            contracts=1
        )
        
        print(f"Strategy: {put_result.strategy}")
        print(f"Break-even: ${put_result.breakeven:.2f}")
        print(f"Max Profit: ${put_result.max_profit:.2f}")
        print(f"Max Loss: ${put_result.max_loss:.2f}")
        print(f"Total Cost: ${put_result.total_cost:.2f}")
        if put_result.probability_of_profit:
            print(f"Probability of Profit: {put_result.probability_of_profit:.1f}%")
        
    finally:
        put_calc.close()
    
    # Multiple Contracts Example
    print("\n3. Multiple Contracts Example (5 Long Calls)")
    print("-" * 45)
    
    multi_calc = LongCallCalculator()
    try:
        multi_result = multi_calc.calculate(
            symbol="AAPL",
            current_price=150.00,
            strike_price=155.00,
            option_premium=3.50,
            expiry_date=expiry_date,
            contracts=5
        )
        
        print(f"Strategy: {multi_result.strategy}")
        print(f"Contracts: {multi_result.contracts}")
        print(f"Break-even: ${multi_result.breakeven:.2f}")
        print(f"Max Loss: ${multi_result.max_loss:.2f}")
        print(f"Total Cost: ${multi_result.total_cost:.2f}")
        
    finally:
        multi_calc.close()
    
    print("\n" + "=" * 50)
    print("Example completed successfully!")


if __name__ == "__main__":
    main()
