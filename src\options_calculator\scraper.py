"""
Web scraping utilities for interacting with optionsprofitcalculator.com
"""

import time
import json
import re
from typing import Dict, Any, Optional, Tuple
from urllib.parse import urljoin, urlparse
import requests
from bs4 import BeautifulSoup
from .exceptions import NetworkError, ParsingError
from .models import CalculatorSession


class OptionsCalculatorScraper:
    """Web scraper for optionsprofitcalculator.com"""
    
    BASE_URL = "https://www.optionsprofitcalculator.com"
    LONG_CALL_URL = f"{BASE_URL}/calculator/long-call.html"
    LONG_PUT_URL = f"{BASE_URL}/calculator/long-put.html"
    
    def __init__(self, delay_between_requests: float = 1.0):
        """
        Initialize the scraper.
        
        Args:
            delay_between_requests: Delay in seconds between requests to be respectful
        """
        self.delay = delay_between_requests
        self.session = requests.Session()
        self.session_state = CalculatorSession()
        
        # Set up headers to mimic a real browser
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def _make_request(self, url: str, method: str = 'GET', data: Optional[Dict] = None, 
                     headers: Optional[Dict] = None) -> requests.Response:
        """
        Make a request with error handling and rate limiting.
        
        Args:
            url: URL to request
            method: HTTP method
            data: POST data if applicable
            headers: Additional headers
            
        Returns:
            Response object
            
        Raises:
            NetworkError: If request fails
        """
        # Rate limiting
        if self.session_state.last_request_time:
            elapsed = time.time() - self.session_state.last_request_time.timestamp()
            if elapsed < self.delay:
                time.sleep(self.delay - elapsed)
        
        try:
            if headers:
                request_headers = {**self.session.headers, **headers}
            else:
                request_headers = self.session.headers
                
            if method.upper() == 'POST':
                response = self.session.post(url, data=data, headers=request_headers, timeout=30)
            else:
                response = self.session.get(url, headers=request_headers, timeout=30)
            
            response.raise_for_status()
            self.session_state.last_request_time = time.time()
            
            return response
            
        except requests.exceptions.RequestException as e:
            raise NetworkError(f"Request failed: {str(e)}")
    
    def _parse_calculator_page(self, html: str) -> Tuple[BeautifulSoup, Dict[str, Any]]:
        """
        Parse calculator page and extract form data and JavaScript variables.
        
        Args:
            html: HTML content
            
        Returns:
            Tuple of (BeautifulSoup object, extracted data dict)
            
        Raises:
            ParsingError: If parsing fails
        """
        try:
            soup = BeautifulSoup(html, 'lxml')
            
            # Extract form data
            form_data = {}
            forms = soup.find_all('form')
            for form in forms:
                inputs = form.find_all(['input', 'select', 'textarea'])
                for input_elem in inputs:
                    name = input_elem.get('name')
                    value = input_elem.get('value', '')
                    if name:
                        form_data[name] = value
            
            # Extract JavaScript variables and configuration
            js_data = {}
            script_tags = soup.find_all('script')
            for script in script_tags:
                if script.string:
                    # Look for configuration objects
                    config_matches = re.findall(r'var\s+(\w+)\s*=\s*({[^}]+})', script.string)
                    for var_name, config_str in config_matches:
                        try:
                            # Simple JSON parsing for configuration
                            config_str = config_str.replace("'", '"')
                            js_data[var_name] = json.loads(config_str)
                        except json.JSONDecodeError:
                            continue
            
            return soup, {'form_data': form_data, 'js_data': js_data}
            
        except Exception as e:
            raise ParsingError(f"Failed to parse calculator page: {str(e)}")
    
    def get_calculator_page(self, calculator_type: str) -> Tuple[BeautifulSoup, Dict[str, Any]]:
        """
        Get and parse a calculator page.
        
        Args:
            calculator_type: 'long-call' or 'long-put'
            
        Returns:
            Tuple of (BeautifulSoup object, extracted data dict)
        """
        if calculator_type == 'long-call':
            url = self.LONG_CALL_URL
        elif calculator_type == 'long-put':
            url = self.LONG_PUT_URL
        else:
            raise ValueError(f"Unknown calculator type: {calculator_type}")
        
        response = self._make_request(url)
        return self._parse_calculator_page(response.text)
    
    def submit_calculation(self, calculator_type: str, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Submit calculation data to the calculator.
        
        Args:
            calculator_type: 'long-call' or 'long-put'
            form_data: Form data to submit
            
        Returns:
            Parsed calculation results
        """
        # This method will need to be implemented based on the actual
        # AJAX endpoints used by the calculator. For now, we'll simulate
        # the calculation using the Black-Scholes model as a fallback.
        
        # In a real implementation, this would:
        # 1. Find the AJAX endpoint used by the calculator
        # 2. Submit the form data via POST request
        # 3. Parse the JSON/HTML response
        # 4. Extract the calculation results
        
        # For now, return a placeholder that will be replaced with
        # actual calculation logic
        return {
            'success': True,
            'results': {},
            'raw_response': ''
        }
    
    def close(self):
        """Close the session."""
        self.session.close()
