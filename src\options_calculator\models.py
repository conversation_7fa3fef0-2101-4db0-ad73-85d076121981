"""
Data models for the Options Calculator API.
"""

from datetime import datetime
from typing import Optional, Dict, List, Any
from pydantic import BaseModel, Field, validator
from decimal import Decimal


class OptionCalculationRequest(BaseModel):
    """Request model for option calculations."""
    
    symbol: str = Field(..., description="Stock symbol (e.g., AAPL)")
    current_price: float = Field(..., gt=0, description="Current stock price")
    strike_price: float = Field(..., gt=0, description="Option strike price")
    option_premium: float = Field(..., gt=0, description="Option premium/cost")
    expiry_date: str = Field(..., description="Expiration date (YYYY-MM-DD)")
    contracts: int = Field(default=1, gt=0, description="Number of contracts")
    implied_volatility: Optional[float] = Field(default=None, description="Implied volatility (optional)")
    
    @validator('symbol')
    def validate_symbol(cls, v):
        if not v or not v.strip():
            raise ValueError("Symbol cannot be empty")
        return v.upper().strip()
    
    @validator('expiry_date')
    def validate_expiry_date(cls, v):
        try:
            datetime.strptime(v, '%Y-%m-%d')
        except ValueError:
            raise ValueError("Expiry date must be in YYYY-MM-DD format")
        return v


class ProfitLossScenario(BaseModel):
    """Model for profit/loss at different stock prices."""
    
    stock_price: float
    profit_loss: float
    profit_loss_percent: float
    option_value: float


class OptionCalculationResult(BaseModel):
    """Result model for option calculations."""
    
    # Basic calculation info
    strategy: str = Field(..., description="Strategy type (Long Call/Long Put)")
    symbol: str = Field(..., description="Stock symbol")
    current_price: float = Field(..., description="Current stock price")
    strike_price: float = Field(..., description="Strike price")
    option_premium: float = Field(..., description="Option premium paid")
    contracts: int = Field(..., description="Number of contracts")
    total_cost: float = Field(..., description="Total cost of the position")
    
    # Key metrics
    breakeven: float = Field(..., description="Break-even stock price")
    max_profit: Optional[float] = Field(default=None, description="Maximum profit (None if unlimited)")
    max_loss: float = Field(..., description="Maximum loss")
    
    # Probability metrics (if available)
    probability_of_profit: Optional[float] = Field(default=None, description="Probability of profit at expiry")
    probability_of_max_profit: Optional[float] = Field(default=None, description="Probability of maximum profit")
    
    # Profit/loss scenarios at different stock prices
    scenarios: List[ProfitLossScenario] = Field(default_factory=list, description="P&L scenarios")
    
    # Additional metrics
    time_decay: Optional[float] = Field(default=None, description="Time decay (theta)")
    delta: Optional[float] = Field(default=None, description="Delta")
    gamma: Optional[float] = Field(default=None, description="Gamma")
    vega: Optional[float] = Field(default=None, description="Vega")
    
    # Raw data from calculator
    raw_data: Dict[str, Any] = Field(default_factory=dict, description="Raw data from calculator")


class CalculatorSession(BaseModel):
    """Model for maintaining calculator session state."""
    
    session_id: Optional[str] = None
    cookies: Dict[str, str] = Field(default_factory=dict)
    headers: Dict[str, str] = Field(default_factory=dict)
    last_request_time: Optional[datetime] = None
