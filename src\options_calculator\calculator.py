"""
Main calculator classes for Long Call and Long Put options.
"""

import math
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from scipy.stats import norm
import numpy as np

from .models import (
    OptionCalculationRequest, 
    OptionCalculationResult, 
    ProfitLossScenario
)
from .scraper import OptionsCalculatorScraper
from .exceptions import ValidationError, CalculationError


class BaseOptionsCalculator:
    """Base class for options calculators."""
    
    def __init__(self):
        self.scraper = OptionsCalculatorScraper()
    
    def _validate_request(self, request: OptionCalculationRequest) -> None:
        """Validate calculation request."""
        if request.current_price <= 0:
            raise ValidationError("Current price must be positive")
        
        if request.strike_price <= 0:
            raise ValidationError("Strike price must be positive")
        
        if request.option_premium <= 0:
            raise ValidationError("Option premium must be positive")
        
        if request.contracts <= 0:
            raise ValidationError("Number of contracts must be positive")
        
        # Validate expiry date is in the future
        try:
            expiry = datetime.strptime(request.expiry_date, '%Y-%m-%d')
            if expiry <= datetime.now():
                raise ValidationError("Expiry date must be in the future")
        except ValueError:
            raise ValidationError("Invalid expiry date format. Use YYYY-MM-DD")
    
    def _calculate_time_to_expiry(self, expiry_date: str) -> float:
        """Calculate time to expiry in years."""
        expiry = datetime.strptime(expiry_date, '%Y-%m-%d')
        now = datetime.now()
        days_to_expiry = (expiry - now).days
        return max(days_to_expiry / 365.0, 0.001)  # Minimum 0.001 years to avoid division by zero
    
    def _black_scholes_call(self, S: float, K: float, T: float, r: float, sigma: float) -> float:
        """Calculate Black-Scholes call option price."""
        if T <= 0:
            return max(S - K, 0)
        
        d1 = (np.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        call_price = S * norm.cdf(d1) - K * np.exp(-r * T) * norm.cdf(d2)
        return max(call_price, 0)
    
    def _black_scholes_put(self, S: float, K: float, T: float, r: float, sigma: float) -> float:
        """Calculate Black-Scholes put option price."""
        if T <= 0:
            return max(K - S, 0)
        
        d1 = (np.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        put_price = K * np.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
        return max(put_price, 0)
    
    def _calculate_implied_volatility(self, option_price: float, S: float, K: float, 
                                    T: float, r: float, option_type: str) -> float:
        """Calculate implied volatility using Newton-Raphson method."""
        # Initial guess
        sigma = 0.3
        
        for _ in range(100):  # Maximum iterations
            if option_type.lower() == 'call':
                price = self._black_scholes_call(S, K, T, r, sigma)
                # Vega calculation for call
                d1 = (np.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * np.sqrt(T))
                vega = S * norm.pdf(d1) * np.sqrt(T)
            else:
                price = self._black_scholes_put(S, K, T, r, sigma)
                # Vega calculation for put (same as call)
                d1 = (np.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * np.sqrt(T))
                vega = S * norm.pdf(d1) * np.sqrt(T)
            
            diff = price - option_price
            
            if abs(diff) < 0.001:  # Convergence threshold
                break
            
            if vega == 0:
                break
            
            sigma = sigma - diff / vega
            sigma = max(sigma, 0.001)  # Minimum volatility
        
        return sigma
    
    def _generate_profit_loss_scenarios(self, request: OptionCalculationRequest, 
                                      option_type: str) -> List[ProfitLossScenario]:
        """Generate profit/loss scenarios at different stock prices."""
        scenarios = []
        
        # Generate price range around current price
        current_price = request.current_price
        price_range = np.linspace(
            current_price * 0.5,  # 50% below current
            current_price * 1.5,  # 50% above current
            21  # 21 data points
        )
        
        total_cost = request.option_premium * request.contracts * 100
        
        for stock_price in price_range:
            if option_type.lower() == 'call':
                # Long call profit/loss
                intrinsic_value = max(stock_price - request.strike_price, 0)
                option_value = intrinsic_value * request.contracts * 100
                profit_loss = option_value - total_cost
            else:
                # Long put profit/loss
                intrinsic_value = max(request.strike_price - stock_price, 0)
                option_value = intrinsic_value * request.contracts * 100
                profit_loss = option_value - total_cost
            
            profit_loss_percent = (profit_loss / total_cost) * 100 if total_cost > 0 else 0
            
            scenarios.append(ProfitLossScenario(
                stock_price=round(stock_price, 2),
                profit_loss=round(profit_loss, 2),
                profit_loss_percent=round(profit_loss_percent, 2),
                option_value=round(option_value, 2)
            ))
        
        return scenarios
    
    def close(self):
        """Close the scraper session."""
        self.scraper.close()


class LongCallCalculator(BaseOptionsCalculator):
    """Calculator for Long Call options."""
    
    def calculate(self, symbol: str, current_price: float, strike_price: float,
                 option_premium: float, expiry_date: str, contracts: int = 1,
                 implied_volatility: Optional[float] = None) -> OptionCalculationResult:
        """
        Calculate Long Call option profit/loss scenarios.
        
        Args:
            symbol: Stock symbol
            current_price: Current stock price
            strike_price: Option strike price
            option_premium: Option premium/cost
            expiry_date: Expiration date (YYYY-MM-DD)
            contracts: Number of contracts (default: 1)
            implied_volatility: Implied volatility (optional)
            
        Returns:
            OptionCalculationResult with calculation results
        """
        # Create and validate request
        request = OptionCalculationRequest(
            symbol=symbol,
            current_price=current_price,
            strike_price=strike_price,
            option_premium=option_premium,
            expiry_date=expiry_date,
            contracts=contracts,
            implied_volatility=implied_volatility
        )
        
        self._validate_request(request)
        
        # Calculate key metrics
        total_cost = option_premium * contracts * 100
        breakeven = strike_price + option_premium
        max_loss = total_cost  # Limited to premium paid
        max_profit = None  # Unlimited for long calls
        
        # Generate profit/loss scenarios
        scenarios = self._generate_profit_loss_scenarios(request, 'call')
        
        # Calculate probability metrics (simplified)
        time_to_expiry = self._calculate_time_to_expiry(expiry_date)
        
        # If implied volatility not provided, estimate it
        if implied_volatility is None:
            implied_volatility = self._calculate_implied_volatility(
                option_premium, current_price, strike_price, time_to_expiry, 0.02, 'call'
            )
        
        # Calculate probability of profit (stock price > breakeven at expiry)
        # Using normal distribution approximation
        d = (np.log(current_price / breakeven) + (0.02 - 0.5 * implied_volatility ** 2) * time_to_expiry) / (implied_volatility * np.sqrt(time_to_expiry))
        probability_of_profit = norm.cdf(d) * 100
        
        return OptionCalculationResult(
            strategy="Long Call",
            symbol=symbol,
            current_price=current_price,
            strike_price=strike_price,
            option_premium=option_premium,
            contracts=contracts,
            total_cost=total_cost,
            breakeven=round(breakeven, 2),
            max_profit=max_profit,
            max_loss=round(max_loss, 2),
            probability_of_profit=round(probability_of_profit, 2),
            scenarios=scenarios,
            raw_data={
                'implied_volatility': round(implied_volatility, 4),
                'time_to_expiry': round(time_to_expiry, 4)
            }
        )


class LongPutCalculator(BaseOptionsCalculator):
    """Calculator for Long Put options."""
    
    def calculate(self, symbol: str, current_price: float, strike_price: float,
                 option_premium: float, expiry_date: str, contracts: int = 1,
                 implied_volatility: Optional[float] = None) -> OptionCalculationResult:
        """
        Calculate Long Put option profit/loss scenarios.
        
        Args:
            symbol: Stock symbol
            current_price: Current stock price
            strike_price: Option strike price
            option_premium: Option premium/cost
            expiry_date: Expiration date (YYYY-MM-DD)
            contracts: Number of contracts (default: 1)
            implied_volatility: Implied volatility (optional)
            
        Returns:
            OptionCalculationResult with calculation results
        """
        # Create and validate request
        request = OptionCalculationRequest(
            symbol=symbol,
            current_price=current_price,
            strike_price=strike_price,
            option_premium=option_premium,
            expiry_date=expiry_date,
            contracts=contracts,
            implied_volatility=implied_volatility
        )
        
        self._validate_request(request)
        
        # Calculate key metrics
        total_cost = option_premium * contracts * 100
        breakeven = strike_price - option_premium
        max_loss = total_cost  # Limited to premium paid
        max_profit = (strike_price - option_premium) * contracts * 100  # Maximum when stock goes to $0
        
        # Generate profit/loss scenarios
        scenarios = self._generate_profit_loss_scenarios(request, 'put')
        
        # Calculate probability metrics (simplified)
        time_to_expiry = self._calculate_time_to_expiry(expiry_date)
        
        # If implied volatility not provided, estimate it
        if implied_volatility is None:
            implied_volatility = self._calculate_implied_volatility(
                option_premium, current_price, strike_price, time_to_expiry, 0.02, 'put'
            )
        
        # Calculate probability of profit (stock price < breakeven at expiry)
        # Using normal distribution approximation
        d = (np.log(current_price / breakeven) + (0.02 - 0.5 * implied_volatility ** 2) * time_to_expiry) / (implied_volatility * np.sqrt(time_to_expiry))
        probability_of_profit = (1 - norm.cdf(d)) * 100
        
        return OptionCalculationResult(
            strategy="Long Put",
            symbol=symbol,
            current_price=current_price,
            strike_price=strike_price,
            option_premium=option_premium,
            contracts=contracts,
            total_cost=total_cost,
            breakeven=round(breakeven, 2),
            max_profit=round(max_profit, 2),
            max_loss=round(max_loss, 2),
            probability_of_profit=round(probability_of_profit, 2),
            scenarios=scenarios,
            raw_data={
                'implied_volatility': round(implied_volatility, 4),
                'time_to_expiry': round(time_to_expiry, 4)
            }
        )
