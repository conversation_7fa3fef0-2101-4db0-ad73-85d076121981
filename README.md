# Options Profit Calculator API

A Python API for programmatically interacting with the options profit calculators at optionsprofitcalculator.com.

## Features

- **Long Call Calculator**: Calculate profit/loss scenarios for long call options
- **Long Put Calculator**: Calculate profit/loss scenarios for long put options
- **Web Scraping**: Uses HTTP requests and HTML parsing (no browser automation)
- **Comprehensive Results**: Returns profit/loss scenarios, break-even points, and probability metrics
- **Interactive Testing**: Includes a comprehensive test script with AAPL examples

## Installation

```bash
pip install -r requirements.txt
```

## Quick Start

```python
from options_calculator import LongCallCalculator, LongPutCalculator

# Long Call Example
call_calc = LongCallCalculator()
call_result = call_calc.calculate(
    symbol="AAPL",
    current_price=150.00,
    strike_price=155.00,
    option_premium=3.50,
    expiry_date="2024-01-19",
    contracts=1
)

print(f"Break-even: ${call_result.breakeven}")
print(f"Max Profit: {call_result.max_profit}")
print(f"Max Loss: ${call_result.max_loss}")

# Long Put Example
put_calc = LongPutCalculator()
put_result = put_calc.calculate(
    symbol="AAPL",
    current_price=150.00,
    strike_price=145.00,
    option_premium=2.75,
    expiry_date="2024-01-19",
    contracts=1
)

print(f"Break-even: ${put_result.breakeven}")
print(f"Max Profit: {put_result.max_profit}")
print(f"Max Loss: ${put_result.max_loss}")
```

## Interactive Testing

Run the interactive test script to test with AAPL:

```bash
python examples/interactive_test.py
```

This will present a menu to:
- Select option strategy (Long Call or Long Put)
- Input parameters for AAPL options
- Display comprehensive results including profit scenarios and probabilities

## API Reference

### LongCallCalculator

Calculate profit/loss scenarios for long call options.

#### Methods

- `calculate(symbol, current_price, strike_price, option_premium, expiry_date, contracts=1, implied_volatility=None)`

### LongPutCalculator

Calculate profit/loss scenarios for long put options.

#### Methods

- `calculate(symbol, current_price, strike_price, option_premium, expiry_date, contracts=1, implied_volatility=None)`

## Requirements

- Python 3.8+
- requests
- beautifulsoup4
- lxml
- httpx
- python-dateutil
- pydantic
- rich

## License

MIT License
