#!/usr/bin/env python3
"""
Interactive test script for the Options Calculator API.
Tests with AAPL options and provides a menu-driven interface.
"""

import sys
import os
from datetime import datetime, timedelta
from typing import Optional

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.prompt import Prompt, FloatPrompt, IntPrompt, Confirm
from rich.text import Text

from options_calculator import LongCallCalculator, LongPutCalculator
from options_calculator.exceptions import ValidationError, CalculationError


class InteractiveOptionsTest:
    """Interactive test interface for options calculations."""
    
    def __init__(self):
        self.console = Console()
        self.call_calculator = LongCallCalculator()
        self.put_calculator = LongPutCalculator()
    
    def display_welcome(self):
        """Display welcome message."""
        welcome_text = Text("Options Profit Calculator API - Interactive Test", style="bold blue")
        self.console.print(Panel(welcome_text, expand=False))
        self.console.print("\nThis tool will help you test options calculations using AAPL as an example.")
        self.console.print("You can input your own parameters or use suggested defaults.\n")
    
    def get_strategy_choice(self) -> str:
        """Get user's strategy choice."""
        self.console.print("[bold]Available Option Strategies:[/bold]")
        self.console.print("1. Long Call (Bullish)")
        self.console.print("2. Long Put (Bearish)")
        
        while True:
            choice = Prompt.ask("\nSelect strategy", choices=["1", "2"])
            if choice == "1":
                return "long_call"
            elif choice == "2":
                return "long_put"
    
    def get_option_parameters(self, strategy: str) -> dict:
        """Get option parameters from user."""
        self.console.print(f"\n[bold]Enter parameters for {strategy.replace('_', ' ').title()}:[/bold]")
        
        # Default values for AAPL
        default_current_price = 150.00
        default_strike_call = 155.00
        default_strike_put = 145.00
        default_premium_call = 3.50
        default_premium_put = 2.75
        default_expiry = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
        
        # Get current stock price
        current_price = FloatPrompt.ask(
            f"Current AAPL stock price",
            default=default_current_price
        )
        
        # Get strike price with strategy-appropriate default
        if strategy == "long_call":
            default_strike = default_strike_call
            strike_hint = "(typically above current price for calls)"
        else:
            default_strike = default_strike_put
            strike_hint = "(typically below current price for puts)"
        
        strike_price = FloatPrompt.ask(
            f"Strike price {strike_hint}",
            default=default_strike
        )
        
        # Get option premium
        if strategy == "long_call":
            default_premium = default_premium_call
        else:
            default_premium = default_premium_put
        
        option_premium = FloatPrompt.ask(
            "Option premium/cost per share",
            default=default_premium
        )
        
        # Get expiry date
        expiry_date = Prompt.ask(
            "Expiration date (YYYY-MM-DD)",
            default=default_expiry
        )
        
        # Get number of contracts
        contracts = IntPrompt.ask(
            "Number of contracts",
            default=1
        )
        
        # Ask about implied volatility
        use_custom_iv = Confirm.ask("Do you want to specify implied volatility?", default=False)
        implied_volatility = None
        if use_custom_iv:
            implied_volatility = FloatPrompt.ask(
                "Implied volatility (as decimal, e.g., 0.25 for 25%)",
                default=0.25
            )
        
        return {
            'symbol': 'AAPL',
            'current_price': current_price,
            'strike_price': strike_price,
            'option_premium': option_premium,
            'expiry_date': expiry_date,
            'contracts': contracts,
            'implied_volatility': implied_volatility
        }
    
    def display_results(self, result, strategy: str):
        """Display calculation results in a formatted table."""
        self.console.print(f"\n[bold green]Results for {result.strategy}:[/bold green]")
        
        # Basic information table
        basic_table = Table(title="Option Details")
        basic_table.add_column("Parameter", style="cyan")
        basic_table.add_column("Value", style="white")
        
        basic_table.add_row("Symbol", result.symbol)
        basic_table.add_row("Current Price", f"${result.current_price:.2f}")
        basic_table.add_row("Strike Price", f"${result.strike_price:.2f}")
        basic_table.add_row("Option Premium", f"${result.option_premium:.2f}")
        basic_table.add_row("Contracts", str(result.contracts))
        basic_table.add_row("Total Cost", f"${result.total_cost:.2f}")
        
        self.console.print(basic_table)
        
        # Key metrics table
        metrics_table = Table(title="Key Metrics")
        metrics_table.add_column("Metric", style="cyan")
        metrics_table.add_column("Value", style="white")
        
        metrics_table.add_row("Break-even Price", f"${result.breakeven:.2f}")
        
        if result.max_profit is None:
            metrics_table.add_row("Maximum Profit", "Unlimited")
        else:
            metrics_table.add_row("Maximum Profit", f"${result.max_profit:.2f}")
        
        metrics_table.add_row("Maximum Loss", f"${result.max_loss:.2f}")
        
        if result.probability_of_profit:
            metrics_table.add_row("Probability of Profit", f"{result.probability_of_profit:.1f}%")
        
        if 'implied_volatility' in result.raw_data:
            iv_percent = result.raw_data['implied_volatility'] * 100
            metrics_table.add_row("Implied Volatility", f"{iv_percent:.1f}%")
        
        self.console.print(metrics_table)
        
        # Profit/Loss scenarios table
        scenarios_table = Table(title="Profit/Loss Scenarios at Expiry")
        scenarios_table.add_column("Stock Price", style="cyan")
        scenarios_table.add_column("Profit/Loss", style="white")
        scenarios_table.add_column("P&L %", style="white")
        scenarios_table.add_column("Option Value", style="white")
        
        # Show key scenarios
        key_scenarios = [
            result.scenarios[0],  # Lowest price
            result.scenarios[len(result.scenarios)//4],  # 25th percentile
            result.scenarios[len(result.scenarios)//2],  # Middle
            result.scenarios[3*len(result.scenarios)//4],  # 75th percentile
            result.scenarios[-1],  # Highest price
        ]
        
        for scenario in key_scenarios:
            profit_color = "green" if scenario.profit_loss >= 0 else "red"
            scenarios_table.add_row(
                f"${scenario.stock_price:.2f}",
                f"[{profit_color}]${scenario.profit_loss:.2f}[/{profit_color}]",
                f"[{profit_color}]{scenario.profit_loss_percent:.1f}%[/{profit_color}]",
                f"${scenario.option_value:.2f}"
            )
        
        self.console.print(scenarios_table)
        
        # Strategy explanation
        if strategy == "long_call":
            explanation = """
[bold]Long Call Strategy:[/bold]
• Bullish strategy - profits when stock price rises above strike + premium
• Limited risk (premium paid) with unlimited profit potential
• Best used when expecting significant upward price movement
            """
        else:
            explanation = """
[bold]Long Put Strategy:[/bold]
• Bearish strategy - profits when stock price falls below strike - premium
• Limited risk (premium paid) with high profit potential (stock can go to $0)
• Best used when expecting significant downward price movement
            """
        
        self.console.print(Panel(explanation.strip(), title="Strategy Notes"))
    
    def run_calculation(self, strategy: str, params: dict):
        """Run the calculation and display results."""
        try:
            if strategy == "long_call":
                result = self.call_calculator.calculate(**params)
            else:
                result = self.put_calculator.calculate(**params)
            
            self.display_results(result, strategy)
            
        except ValidationError as e:
            self.console.print(f"[red]Validation Error: {e}[/red]")
        except CalculationError as e:
            self.console.print(f"[red]Calculation Error: {e}[/red]")
        except Exception as e:
            self.console.print(f"[red]Unexpected Error: {e}[/red]")
    
    def run(self):
        """Run the interactive test."""
        self.display_welcome()
        
        try:
            while True:
                # Get strategy choice
                strategy = self.get_strategy_choice()
                
                # Get parameters
                params = self.get_option_parameters(strategy)
                
                # Run calculation
                self.run_calculation(strategy, params)
                
                # Ask if user wants to continue
                if not Confirm.ask("\nWould you like to run another calculation?", default=True):
                    break
                
                self.console.print("\n" + "="*60 + "\n")
        
        finally:
            # Clean up
            self.call_calculator.close()
            self.put_calculator.close()
        
        self.console.print("\n[bold blue]Thank you for using the Options Calculator API![/bold blue]")


if __name__ == "__main__":
    test = InteractiveOptionsTest()
    test.run()
