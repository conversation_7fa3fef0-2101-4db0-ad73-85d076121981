"""
Command-line interface for the Options Calculator API.
"""

import argparse
import sys
from datetime import datetime, timedelta
from .calculator import LongCallCalculator, LongPutCalculator
from .exceptions import ValidationError, CalculationError


def create_parser():
    """Create command-line argument parser."""
    parser = argparse.ArgumentParser(
        description="Options Profit Calculator API - Command Line Interface"
    )
    
    parser.add_argument(
        "strategy",
        choices=["long-call", "long-put"],
        help="Option strategy to calculate"
    )
    
    parser.add_argument(
        "--symbol", "-s",
        required=True,
        help="Stock symbol (e.g., AAPL)"
    )
    
    parser.add_argument(
        "--current-price", "-c",
        type=float,
        required=True,
        help="Current stock price"
    )
    
    parser.add_argument(
        "--strike-price", "-k",
        type=float,
        required=True,
        help="Option strike price"
    )
    
    parser.add_argument(
        "--premium", "-p",
        type=float,
        required=True,
        help="Option premium/cost per share"
    )
    
    parser.add_argument(
        "--expiry", "-e",
        required=True,
        help="Expiration date (YYYY-MM-DD)"
    )
    
    parser.add_argument(
        "--contracts", "-n",
        type=int,
        default=1,
        help="Number of contracts (default: 1)"
    )
    
    parser.add_argument(
        "--implied-volatility", "-iv",
        type=float,
        help="Implied volatility (as decimal, e.g., 0.25 for 25%%)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Show detailed profit/loss scenarios"
    )
    
    return parser


def print_results(result, verbose=False):
    """Print calculation results."""
    print(f"\n{result.strategy} Results for {result.symbol}")
    print("=" * 50)
    
    print(f"Current Price: ${result.current_price:.2f}")
    print(f"Strike Price: ${result.strike_price:.2f}")
    print(f"Option Premium: ${result.option_premium:.2f}")
    print(f"Contracts: {result.contracts}")
    print(f"Total Cost: ${result.total_cost:.2f}")
    
    print(f"\nKey Metrics:")
    print(f"Break-even Price: ${result.breakeven:.2f}")
    
    if result.max_profit is None:
        print(f"Maximum Profit: Unlimited")
    else:
        print(f"Maximum Profit: ${result.max_profit:.2f}")
    
    print(f"Maximum Loss: ${result.max_loss:.2f}")
    
    if result.probability_of_profit:
        print(f"Probability of Profit: {result.probability_of_profit:.1f}%")
    
    if 'implied_volatility' in result.raw_data:
        iv_percent = result.raw_data['implied_volatility'] * 100
        print(f"Implied Volatility: {iv_percent:.1f}%")
    
    if verbose and result.scenarios:
        print(f"\nProfit/Loss Scenarios at Expiry:")
        print(f"{'Stock Price':<12} {'Profit/Loss':<12} {'P&L %':<8} {'Option Value':<12}")
        print("-" * 50)
        
        # Show every 3rd scenario to avoid too much output
        for i, scenario in enumerate(result.scenarios):
            if i % 3 == 0:  # Show every 3rd scenario
                profit_symbol = "+" if scenario.profit_loss >= 0 else ""
                print(f"${scenario.stock_price:<11.2f} "
                      f"{profit_symbol}${scenario.profit_loss:<11.2f} "
                      f"{scenario.profit_loss_percent:<7.1f}% "
                      f"${scenario.option_value:<11.2f}")


def main():
    """Main CLI function."""
    parser = create_parser()
    args = parser.parse_args()
    
    try:
        # Create calculator
        if args.strategy == "long-call":
            calculator = LongCallCalculator()
        else:
            calculator = LongPutCalculator()
        
        # Run calculation
        result = calculator.calculate(
            symbol=args.symbol,
            current_price=args.current_price,
            strike_price=args.strike_price,
            option_premium=args.premium,
            expiry_date=args.expiry,
            contracts=args.contracts,
            implied_volatility=args.implied_volatility
        )
        
        # Print results
        print_results(result, verbose=args.verbose)
        
        # Clean up
        calculator.close()
        
    except ValidationError as e:
        print(f"Validation Error: {e}", file=sys.stderr)
        sys.exit(1)
    except CalculationError as e:
        print(f"Calculation Error: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
