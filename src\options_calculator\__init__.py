"""
Options Profit Calculator API

A Python API for programmatically interacting with the options profit calculators
at optionsprofitcalculator.com.
"""

from .calculator import LongCallCalculator, LongPutCalculator
from .models import OptionCalculationRequest, OptionCalculationResult
from .exceptions import CalculatorError, ValidationError, NetworkError

__version__ = "1.0.0"
__author__ = "Options Calculator API"

__all__ = [
    "LongCallCalculator",
    "LongPutCalculator", 
    "OptionCalculationRequest",
    "OptionCalculationResult",
    "CalculatorError",
    "ValidationError",
    "NetworkError",
]
