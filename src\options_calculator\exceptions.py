"""
Custom exceptions for the Options Calculator API.
"""


class CalculatorError(Exception):
    """Base exception for calculator-related errors."""
    pass


class ValidationError(CalculatorError):
    """Raised when input validation fails."""
    pass


class NetworkError(CalculatorError):
    """Raised when network requests fail."""
    pass


class ParsingError(CalculatorError):
    """Raised when HTML parsing fails."""
    pass


class CalculationError(CalculatorError):
    """Raised when calculation fails on the remote server."""
    pass
